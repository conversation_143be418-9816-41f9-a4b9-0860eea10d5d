import asyncio
import json
import uuid
import time
from typing import Dict, Set, Optional
from config.logger import setup_logging
from core.mqtt_connection import MqttConnectionHandler

TAG = __name__


class MqttServer:
    """MQTT 服务器，复刻 WebSocket 服务器的功能"""
    
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging()
        self.active_connections: Set[MqttConnectionHandler] = set()
        self.client_topics: Dict[str, str] = {}  # client_id -> topic mapping
        self.topic_clients: Dict[str, str] = {}  # topic -> client_id mapping
        
        # 初始化组件（与 WebSocket 服务器相同）
        self._vad = None
        self._asr = None
        self._llm = None
        self._memory = None
        self._intent = None
        
        # MQTT broker 相关
        self.broker_host = "0.0.0.0"
        self.broker_port = 1883
        self.running = False
        
    def set_components(self, vad, asr, llm, memory, intent):
        """设置处理组件"""
        self._vad = vad
        self._asr = asr
        self._llm = llm
        self._memory = memory
        self._intent = intent
        
    async def start(self):
        """启动 MQTT 服务器"""
        # 直接从传入的 config 中获取 MQTT 配置
        mqtt_config = self.config.get("mqtt", {"host": "0.0.0.0", "port": 1883})

        self.broker_host = mqtt_config.get("host", "0.0.0.0")
        self.broker_port = int(mqtt_config.get("port", 1883))

        self.logger.bind(tag=TAG).info(f"启动 MQTT 服务器在 {self.broker_host}:{self.broker_port}")

        # 启动简单的 MQTT broker
        await self._start_mqtt_broker()
        
    async def _start_mqtt_broker(self):
        """启动 MQTT broker"""
        try:
            # 创建 TCP 服务器来处理 MQTT 连接
            server = await asyncio.start_server(
                self._handle_mqtt_connection,
                self.broker_host,
                self.broker_port
            )
            
            self.running = True
            self.logger.bind(tag=TAG).info(f"MQTT broker 已启动在 {self.broker_host}:{self.broker_port}")
            
            async with server:
                await server.serve_forever()
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"启动 MQTT broker 失败: {e}")
            raise
            
    async def _handle_mqtt_connection(self, reader, writer):
        """处理新的 MQTT 连接"""
        client_address = writer.get_extra_info('peername')
        self.logger.bind(tag=TAG).info(f"新的 MQTT 连接来自: {client_address}")
        
        # 创建连接处理器
        handler = MqttConnectionHandler(
            self.config,
            self._vad,
            self._asr,
            self._llm,
            self._memory,
            self._intent,
            self,
            reader,
            writer
        )
        
        self.active_connections.add(handler)
        
        try:
            await handler.handle_connection()
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理 MQTT 连接时出错: {e}")
        finally:
            # 清理连接
            self.active_connections.discard(handler)
            if not writer.is_closing():
                writer.close()
                await writer.wait_closed()
                
    def register_client_topic(self, client_id: str, topic: str):
        """注册客户端主题映射"""
        self.client_topics[client_id] = topic
        self.topic_clients[topic] = client_id
        
    def unregister_client(self, client_id: str):
        """注销客户端"""
        if client_id in self.client_topics:
            topic = self.client_topics[client_id]
            del self.client_topics[client_id]
            if topic in self.topic_clients:
                del self.topic_clients[topic]
                
    async def publish_to_client(self, client_id: str, message: dict):
        """向指定客户端发布消息"""
        if client_id not in self.client_topics:
            self.logger.bind(tag=TAG).warning(f"客户端 {client_id} 未找到对应主题")
            return False
            
        topic = self.client_topics[client_id]
        
        # 查找对应的连接处理器
        for handler in self.active_connections:
            if handler.client_id == client_id:
                await handler.send_message(topic, json.dumps(message))
                return True
                
        self.logger.bind(tag=TAG).warning(f"客户端 {client_id} 的连接处理器未找到")
        return False
        
    async def broadcast_message(self, message: dict):
        """广播消息到所有连接的客户端"""
        for handler in self.active_connections:
            try:
                if handler.client_id and handler.client_id in self.client_topics:
                    topic = self.client_topics[handler.client_id]
                    await handler.send_message(topic, json.dumps(message))
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"广播消息到客户端 {handler.client_id} 失败: {e}")
                
    def get_active_connections_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
        
    async def stop(self):
        """停止 MQTT 服务器"""
        self.running = False
        
        # 关闭所有活跃连接
        for handler in list(self.active_connections):
            try:
                await handler.close()
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"关闭连接处理器失败: {e}")
                
        self.active_connections.clear()
        self.client_topics.clear()
        self.topic_clients.clear()
        
        self.logger.bind(tag=TAG).info("MQTT 服务器已停止")
