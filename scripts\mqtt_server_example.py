#!/usr/bin/env python3
"""
MQTT Server 示例，用于与 XiaoZhi ESP32 设备通信
"""

import json
import paho.mqtt.client as mqtt
import socket
import threading
import time
import random
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util import Counter


class XiaoZhiMqttServer:
    def __init__(self, broker_address="localhost", broker_port=1883):
        self.broker_address = broker_address
        self.broker_port = broker_port
        self.client = mqtt.Client()
        self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.udp_port = 9001  # UDP服务端口
        self.sessions = {}  # 存储会话信息
        
        # 设置MQTT回调
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        
        # 设置订阅主题
        self.subscribe_topic = "xiaozhi/esp32"
        self.publish_topic = "xiaozhi/esp32"

    def on_connect(self, client, userdata, flags, rc):
        print(f"Connected to MQTT broker with result code {rc}")
        # 订阅来自ESP32的主题
        client.subscribe(self.subscribe_topic)
        print(f"Subscribed to topic: {self.subscribe_topic}")

    def on_disconnect(self, client, userdata, rc):
        print("Disconnected from MQTT broker")

    def on_message(self, client, userdata, msg):
        """处理来自ESP32的消息"""
        try:
            payload = msg.payload.decode('utf-8')
            print(f"Received message: {payload}")
            
            data = json.loads(payload)
            msg_type = data.get("type")
            
            if msg_type == "hello":
                self.handle_hello(client, data)
            elif msg_type == "goodbye":
                self.handle_goodbye(client, data)
            else:
                print(f"Received unknown message type: {msg_type}")
                
        except json.JSONDecodeError:
            print(f"Failed to decode JSON: {msg.payload}")
        except Exception as e:
            print(f"Error processing message: {e}")

    def handle_hello(self, client, data):
        """处理ESP32的hello消息，建立UDP通道"""
        print("Handling hello request from ESP32")
        
        # 生成会话ID
        session_id = ''.join(random.choices('0123456789abcdef', k=32))
        
        # 生成AES密钥和nonce
        aes_key = ''.join(random.choices('0123456789abcdef', k=32))  # 128位密钥
        aes_nonce = ''.join(random.choices('0123456789abcdef', k=32))  # 128位nonce
        
        # 准备响应数据
        response = {
            "type": "hello",
            "session_id": session_id,
            "transport": "udp",
            "audio_params": {
                "sample_rate": 16000,
                "frame_duration": 20
            },
            "udp": {
                "server": self.broker_address,
                "port": self.udp_port,
                "key": aes_key,
                "nonce": aes_nonce
            }
        }
        
        # 存储会话信息
        self.sessions[session_id] = {
            "key": aes_key,
            "nonce": aes_nonce,
            "created_at": time.time()
        }
        
        # 发送响应
        client.publish(self.publish_topic, json.dumps(response))
        print(f"Sent hello response with session ID: {session_id}")
        
        # 启动UDP监听线程（如果尚未启动）
        if not hasattr(self, 'udp_thread') or not self.udp_thread.is_alive():
            self.udp_thread = threading.Thread(target=self.listen_udp)
            self.udp_thread.daemon = True
            self.udp_thread.start()

    def handle_goodbye(self, client, data):
        """处理ESP32的goodbye消息"""
        session_id = data.get("session_id")
        if session_id in self.sessions:
            del self.sessions[session_id]
            print(f"Session {session_id} closed")
        else:
            print(f"Unknown session ID: {session_id}")

    def listen_udp(self):
        """监听UDP数据包"""
        self.udp_socket.bind(('', self.udp_port))
        print(f"Listening for UDP packets on port {self.udp_port}")
        
        while True:
            try:
                data, addr = self.udp_socket.recvfrom(4096)
                print(f"Received UDP packet from {addr}, size: {len(data)} bytes")
                # 这里可以处理接收到的音频数据
                # 解密等操作可以在这里完成
                
            except Exception as e:
                print(f"Error receiving UDP data: {e}")

    def send_audio_to_esp32(self, session_id, audio_data):
        """向ESP32发送音频数据"""
        if session_id not in self.sessions:
            print(f"Session {session_id} not found")
            return
            
        session = self.sessions[session_id]
        
        # 这里应该实现音频数据的加密和发送
        # 由于涉及加密细节，这里只是示意
        print(f"Sending audio data to session {session_id}")

    def start(self):
        """启动MQTT客户端"""
        try:
            self.client.connect(self.broker_address, self.broker_port, 60)
            print(f"Connecting to MQTT broker at {self.broker_address}:{self.broker_port}")
            
            # 开始MQTT循环
            self.client.loop_forever()
            
        except Exception as e:
            print(f"Failed to connect to MQTT broker: {e}")

    def stop(self):
        """停止客户端"""
        self.client.disconnect()
        self.udp_socket.close()


if __name__ == "__main__":
    # 创建并启动服务器
    server = XiaoZhiMqttServer("localhost", 1883)
    
    try:
        print("Starting MQTT server for XiaoZhi ESP32 device...")
        server.start()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.stop()